import java.io.*;
import java.util.zip.*;

public class UnzipUtil {
    public static void main(String[] args) {
        String zipFilePath = "gj_dh_res.zip";
        String destDirectory = "temp_extract";
        
        try {
            unzip(zipFilePath, destDirectory);
            System.out.println("解压完成！");
        } catch (IOException e) {
            System.err.println("解压失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public static void unzip(String zipFilePath, String destDirectory) throws IOException {
        File destDir = new File(destDirectory);
        if (!destDir.exists()) {
            destDir.mkdirs();
        }
        
        try (ZipInputStream zipIn = new ZipInputStream(new FileInputStream(zipFilePath))) {
            ZipEntry entry = zipIn.getNextEntry();
            
            while (entry != null) {
                String filePath = destDirectory + File.separator + entry.getName();
                
                if (!entry.isDirectory()) {
                    extractFile(zipIn, filePath);
                } else {
                    File dir = new File(filePath);
                    dir.mkdirs();
                }
                
                zipIn.closeEntry();
                entry = zipIn.getNextEntry();
                
                System.out.println("解压: " + entry != null ? entry.getName() : "完成");
            }
        }
    }
    
    private static void extractFile(ZipInputStream zipIn, String filePath) throws IOException {
        File file = new File(filePath);
        File parent = file.getParentFile();
        if (parent != null && !parent.exists()) {
            parent.mkdirs();
        }
        
        try (BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(filePath))) {
            byte[] bytesIn = new byte[4096];
            int read = 0;
            while ((read = zipIn.read(bytesIn)) != -1) {
                bos.write(bytesIn, 0, read);
            }
        }
    }
}
