/**
 * 文件名: AssetsUtil.java
 * 功能描述: 负责从assets目录复制基础配置文件到外部存储，避免每次启动都需要网络下载
 * 创建日期: 2025-07-31
 * 作者: AI Assistant
 * 模块归属: duix-sdk工具类
 */
package ai.guiji.duix.sdk.client.util;

import android.content.Context;
import android.content.res.AssetManager;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * Assets文件管理工具类
 * 用于处理从assets目录复制基础配置文件到外部存储
 */
public class AssetsUtil {

    private static final String TAG = "AssetsUtil";
    
    /**
     * 检查并复制基础配置文件从assets到外部存储
     * @param context 应用上下文
     * @return 是否成功复制或已存在
     */
    public static boolean ensureBaseConfigFromAssets(Context context) {
        try {
            String duixDir = context.getExternalFilesDir("duix").getAbsolutePath();
            File baseDir = new File(duixDir + "/model", "gj_dh_res");
            File baseTag = new File(duixDir + "/model/tmp", "gj_dh_res");
            
            // 如果已经存在，直接返回true
            if (baseDir.exists() && baseTag.exists()) {
                Logger.d(TAG, "基础配置文件已存在，无需复制");
                return true;
            }
            
            // 检查assets中是否有基础配置文件
            if (hasBaseConfigInAssets(context)) {
                Logger.d(TAG, "开始从assets复制基础配置文件");
                return copyBaseConfigFromAssets(context, baseDir, baseTag);
            } else {
                Logger.d(TAG, "assets中未找到基础配置文件");
                return false;
            }
        } catch (Exception e) {
            Logger.e(TAG, "检查基础配置文件时发生错误: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 检查assets中是否存在基础配置文件
     * @param context 应用上下文
     * @return 是否存在
     */
    private static boolean hasBaseConfigInAssets(Context context) {
        try {
            AssetManager assetManager = context.getAssets();
            String[] files = assetManager.list("duix");
            if (files != null) {
                for (String file : files) {
                    if ("gj_dh_res".equals(file)) {
                        return true;
                    }
                }
            }
        } catch (IOException e) {
            Logger.e(TAG, "检查assets文件时发生错误: " + e.getMessage());
        }
        return false;
    }
    
    /**
     * 从assets复制基础配置文件到外部存储
     * @param context 应用上下文
     * @param baseDir 目标基础目录
     * @param baseTag 标记文件目录
     * @return 是否复制成功
     */
    private static boolean copyBaseConfigFromAssets(Context context, File baseDir, File baseTag) {
        try {
            // 确保目标目录存在
            if (!baseDir.exists()) {
                baseDir.mkdirs();
            }
            
            // 递归复制assets/duix/gj_dh_res目录下的所有文件
            boolean success = copyAssetsFolder(context, "duix/gj_dh_res", baseDir.getAbsolutePath());
            
            if (success) {
                // 创建标记文件
                File tmpDir = baseTag.getParentFile();
                if (!tmpDir.exists()) {
                    tmpDir.mkdirs();
                }
                if (!baseTag.exists()) {
                    baseTag.mkdirs();
                }
                Logger.d(TAG, "基础配置文件复制完成");
                return true;
            } else {
                Logger.e(TAG, "基础配置文件复制失败");
                return false;
            }
        } catch (Exception e) {
            Logger.e(TAG, "复制基础配置文件时发生错误: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 递归复制assets文件夹
     * @param context 应用上下文
     * @param assetsPath assets中的路径
     * @param targetPath 目标路径
     * @return 是否复制成功
     */
    private static boolean copyAssetsFolder(Context context, String assetsPath, String targetPath) {
        try {
            AssetManager assetManager = context.getAssets();
            String[] files = assetManager.list(assetsPath);
            
            if (files == null || files.length == 0) {
                // 这是一个文件，直接复制
                return copyAssetFile(context, assetsPath, targetPath);
            } else {
                // 这是一个目录，递归复制
                File targetDir = new File(targetPath);
                if (!targetDir.exists()) {
                    targetDir.mkdirs();
                }
                
                for (String file : files) {
                    String subAssetsPath = assetsPath + "/" + file;
                    String subTargetPath = targetPath + "/" + file;
                    if (!copyAssetsFolder(context, subAssetsPath, subTargetPath)) {
                        return false;
                    }
                }
                return true;
            }
        } catch (IOException e) {
            Logger.e(TAG, "复制assets文件夹时发生错误: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 复制单个assets文件
     * @param context 应用上下文
     * @param assetsPath assets中的文件路径
     * @param targetPath 目标文件路径
     * @return 是否复制成功
     */
    private static boolean copyAssetFile(Context context, String assetsPath, String targetPath) {
        try {
            AssetManager assetManager = context.getAssets();
            InputStream inputStream = assetManager.open(assetsPath);
            
            File targetFile = new File(targetPath);
            File parentDir = targetFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            FileOutputStream outputStream = new FileOutputStream(targetFile);
            
            byte[] buffer = new byte[4096];
            int length;
            while ((length = inputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, length);
            }
            
            outputStream.flush();
            outputStream.close();
            inputStream.close();
            
            Logger.d(TAG, "文件复制成功: " + assetsPath + " -> " + targetPath);
            return true;
        } catch (IOException e) {
            Logger.e(TAG, "复制文件时发生错误: " + assetsPath + " -> " + targetPath + ", 错误: " + e.getMessage());
            return false;
        }
    }
}
