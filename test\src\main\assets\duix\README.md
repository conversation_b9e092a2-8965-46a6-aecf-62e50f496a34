# Duix 基础配置文件内置说明

## 概述

为了提升用户体验，避免每次启动应用都需要下载基础配置文件，我们在 `assets/duix/gj_dh_res/` 目录中内置了基础配置文件。

## 目录结构

```
test/src/main/assets/duix/
├── README.md                 # 本说明文件
└── gj_dh_res/               # 基础配置文件目录
    ├── [基础配置文件]        # 从 gj_dh_res.zip 解压得到的文件
    └── [其他必需文件]
```

## 工作原理

1. **首次检查**: 应用启动时，`VirtualModelUtil.checkBaseConfig()` 会首先检查外部存储中是否已存在基础配置文件
2. **Assets复制**: 如果外部存储中不存在，会自动从 `assets/duix/gj_dh_res/` 复制文件到外部存储
3. **网络下载**: 只有当assets中也没有文件时，才会进行网络下载

## 如何添加基础配置文件

1. 下载 `gj_dh_res.zip` 文件
2. 解压到 `test/src/main/assets/duix/gj_dh_res/` 目录
3. 确保所有必需的文件都已正确放置

## 优势

- **快速启动**: 首次启动无需等待网络下载
- **离线可用**: 即使没有网络连接也能正常使用基础功能
- **自动回退**: 如果assets中没有文件，会自动回退到网络下载
- **体积控制**: 只内置必需的基础配置文件，不会显著增加APK体积

## 注意事项

- 基础配置文件更新时，需要重新打包assets
- 确保assets中的文件版本与代码兼容
- 建议定期检查基础配置文件的更新

## 相关代码文件

- `VirtualModelUtil.java`: 主要的检查和下载逻辑
- `AssetsUtil.java`: Assets文件复制工具类
- `Constant.java`: 基础配置文件下载URL定义
