package ai.guiji.duix.sdk.client;

import android.content.Context;
import android.text.TextUtils;

import java.io.File;

import ai.guiji.duix.sdk.client.net.DownloadZipService;
import ai.guiji.duix.sdk.client.util.AssetsUtil;


public class VirtualModelUtil {

    /**
     * 检查基础配置文件是否存在
     * 优先检查外部存储，如果不存在则尝试从assets复制
     * @param context 应用上下文
     * @return 基础配置文件是否可用
     */
    public static boolean checkBaseConfig(Context context){
        String duixDir = context.getExternalFilesDir("duix").getAbsolutePath();
        File baseDir = new File(duixDir + "/model", "gj_dh_res");
        File baseTag = new File(duixDir + "/model/tmp", "gj_dh_res");

        // 首先检查外部存储中是否已存在
        if (baseDir.exists() && baseTag.exists()) {
            return true;
        }

        // 如果外部存储中不存在，尝试从assets复制
        return AssetsUtil.ensureBaseConfigFromAssets(context);
    }

    public static boolean checkModel(Context context, String name){
        if (!TextUtils.isEmpty(name)){
            String duixDir = context.getExternalFilesDir("duix").getAbsolutePath();
            if (name.startsWith("https://") || name.startsWith("http://")){
                String dirName = "";
                try {
                    dirName = name.substring(name.lastIndexOf("/") + 1).replace(".zip", "");
                }catch (Exception ignore){
                }
                if (!TextUtils.isEmpty(dirName)){
                    File modelDir = new File(duixDir + "/model", dirName);
                    File modelTag = new File(duixDir + "/model/tmp", dirName);
                    return modelDir.exists() && modelTag.exists();
                } else {
                    return false;
                }
            } else {
                File modelDir = new File(duixDir + "/model", name);
                File modelTag = new File(duixDir + "/model/tmp", name);
                return modelDir.exists() && modelTag.exists();
            }
        } else {
            return false;
        }
    }

    /**
     * 基础配置文件下载（使用默认URL）
     * 在下载前会先检查assets中是否有内置文件
     * @param context 应用上下文
     * @param callback 下载回调
     */
    public static void baseConfigDownload(Context context, ModelDownloadCallback callback){
        // 首先尝试从assets复制
        if (AssetsUtil.ensureBaseConfigFromAssets(context)) {
            // 如果assets复制成功，直接回调成功
            String duixDir = context.getExternalFilesDir("duix").getAbsolutePath();
            File baseDir = new File(duixDir + "/model", "gj_dh_res");
            if (callback != null) {
                callback.onDownloadComplete(Constant.BASE_DOWNLOAD_URL, baseDir);
            }
            return;
        }

        // 如果assets中没有文件，则进行网络下载
        String url = Constant.BASE_DOWNLOAD_URL;
        baseConfigDownload(context, url, callback);
    }
    /**
     * 基础配置文件下载
     */
    public static void baseConfigDownload(Context context, String url, ModelDownloadCallback callback){
        String duixDir = context.getExternalFilesDir("duix").getAbsolutePath();
        File baseDir = new File(duixDir + "/model", "gj_dh_res");
        DownloadZipService.downloadAndUnzip(context, url, baseDir, new DownloadZipService.Callback() {
            @Override
            public void onDownloadProgress(long current, long total) {
                if (callback != null){
                    callback.onDownloadProgress(url, current, total);
                }
            }

            @Override
            public void onUnzipProgress(long current, long total) {
                if (callback != null){
                    callback.onUnzipProgress(url, current, total);
                }
            }

            @Override
            public void onComplete(File baseDirFile) {
                // init model
                if (callback != null){
                    callback.onDownloadComplete(url, baseDirFile);
                }
            }

            @Override
            public void onError(int code, String msg) {
                if (callback != null){
                    callback.onDownloadFail(url, code, msg);
                }
            }
        }, true);
    }

    /**
     * 模型文件下载
     */
    public static void modelDownload(Context context, String modelUrl, ModelDownloadCallback callback){
        String duixDir = context.getExternalFilesDir("duix").getAbsolutePath();
        if (!TextUtils.isEmpty(modelUrl) && (modelUrl.startsWith("https://") || modelUrl.startsWith("http://"))){
            String dirName = "";
            try {
                dirName = modelUrl.substring(modelUrl.lastIndexOf("/") + 1).replace(".zip", "");
            }catch (Exception ignore){
            }
            if (!TextUtils.isEmpty(dirName)){
                File modelDir = new File(duixDir + "/model", dirName);
                // 下载模型文件
                DownloadZipService.downloadAndUnzip(context, modelUrl, modelDir, new DownloadZipService.Callback() {
                    @Override
                    public void onDownloadProgress(long current, long total) {
                        if (callback != null){
                            callback.onDownloadProgress(modelUrl, current, total);
                        }
                    }

                    @Override
                    public void onUnzipProgress(long current, long total) {
                        if (callback != null){
                            callback.onUnzipProgress(modelUrl, current, total);
                        }
                    }

                    @Override
                    public void onComplete(File modelFile) {
                        // init model
                        if (callback != null){
                            callback.onDownloadComplete(modelUrl, modelFile);
                        }
                    }

                    @Override
                    public void onError(int code, String msg) {
                        if (callback != null){
                            callback.onDownloadFail(modelUrl, code, msg);
                        }
                    }
                }, true);
            } else {
                if (callback != null){
                    callback.onDownloadFail(modelUrl, -1004, "Illegal model url[" + modelUrl + "]");
                }
            }
        } else {
            if (callback != null){
                callback.onDownloadFail(modelUrl, -1003, "Illegal download url[" + modelUrl + "]");
            }
        }
    }

    public interface ModelDownloadCallback {

        void onDownloadProgress(String url, long current, long total);

        void onUnzipProgress(String url, long current, long total);

        void onDownloadComplete(String url, File dir);

        /**
         * -1000    Compressed file download failed
         * -1001    An exception occurred while decompressing the file
         * -1002    Target folder not found
         * -1003    Illegal download url
         * -1004    Illegal model url
         * -1005    Service not initialized
         */
        void onDownloadFail(String url, int code, String msg);
    }
}
